import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import Modal from '@/components/Modal';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';

// Notifications sample data
const sampleNotifications = [
  { id: 1, name: 'Temperature Alert', timestamp: '2025-01-15T10:00:00.000Z', parameters: '{"temperature":24}' },
  { id: 2, name: 'Light On', timestamp: '2025-01-15T10:05:00.000Z', parameters: '{"brightness":100}' },
  { id: 3, name: '<PERSON> Locked', timestamp: '2025-01-15T10:10:00.000Z', parameters: '{"status":"locked"}' },
  { id: 4, name: 'Camera Activated', timestamp: '2025-01-15T10:15:00.000Z', parameters: '{"resolution":"1080p"}' },
  { id: 5, name: 'Music Playing', timestamp: '2025-01-15T10:20:00.000Z', parameters: '{"song":"Favorite Song"}' },
  { id: 6, name: 'Plug Turned Off', timestamp: '2025-01-15T10:25:00.000Z', parameters: '{"power":"off"}' },
  { id: 7, name: 'Smoke Detector Tested', timestamp: '2025-01-15T10:30:00.000Z', parameters: '{"status":"normal"}' },
  { id: 8, name: 'Doorbell Status', timestamp: '2025-01-15T10:35:00.000Z', parameters: '{"status":"active"}' },
  { id: 9, name: 'Refrigerator Temperature Set', timestamp: '2025-01-15T10:40:00.000Z', parameters: '{"temperature":4}' },
  { id: 10, name: 'Air Purifier Running', timestamp: '2025-01-15T10:45:00.000Z', parameters: '{"airQuality":"good"}' },
];

// Commands sample data
const sampleCommands = [
  { id: 1, name: 'Adjust Temperature', timestamp: '2025-01-15T09:00:00.000Z', parameters: '{"temperature":24}', status: 'completed', result: '{"success":true}' },
  { id: 2, name: 'Turn On Light', timestamp: '2025-01-15T09:05:00.000Z', parameters: '{"brightness":100}', status: 'completed', result: '{"success":true}' },
  { id: 3, name: 'Lock Door', timestamp: '2025-01-15T09:10:00.000Z', parameters: '{"status":"locked"}', status: 'failed', result: '{"success":false,"error":"Low battery"}' },
  { id: 4, name: 'Activate Camera', timestamp: '2025-01-15T09:15:00.000Z', parameters: '{"resolution":"1080p"}', status: 'in progress', result: 'null' },
  { id: 5, name: 'Play Music', timestamp: '2025-01-15T09:20:00.000Z', parameters: '{"song":"Favorite Song"}', status: 'completed', result: '{"success":true}' },
  { id: 6, name: 'Turn Off Plug', timestamp: '2025-01-15T09:25:00.000Z', parameters: '{"power":"off"}', status: 'completed', result: '{"success":true}' },
  { id: 7, name: 'Test Smoke Detector', timestamp: '2025-01-15T09:30:00.000Z', parameters: '{"test":true}', status: 'in progress', result: 'null' },
  { id: 8, name: 'Check Doorbell Status', timestamp: '2025-01-15T09:35:00.000Z', parameters: '{"status":"active"}', status: 'completed', result: '{"success":true}' },
  { id: 9, name: 'Set Refrigerator Temperature', timestamp: '2025-01-15T09:40:00.000Z', parameters: '{"temperature":4}', status: 'completed', result: '{"success":true}' },
  { id: 10, name: 'Run Air Purifier', timestamp: '2025-01-15T09:45:00.000Z', parameters: '{"duration":120}', status: 'completed', result: '{"success":true}' },
];

const DeviceDetail = () => {
  const { deviceId } = useParams<{ deviceId: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'dashboard' | 'edit' | 'commands' | 'notifications'>('dashboard');
  const [isNotificationModalOpen, setIsNotificationModalOpen] = useState(false);
  const [isCommandModalOpen, setIsCommandModalOpen] = useState(false);
  const [newNotification, setNewNotification] = useState({ name: '', parameters: '' });
  const [newCommand, setNewCommand] = useState({ name: '', parameters: '' });

  // In a real app, you'd fetch device data based on deviceId
  // For demo, we'll just simulate

  const handleSendNotification = () => {
    setIsNotificationModalOpen(true);
  };

  const handleSendCommand = () => {
    setIsCommandModalOpen(true);
  };

  const handleSaveNotification = () => {
    // In a real app, you would call an API here
    alert(`Notification sent: ${newNotification.name}`);
    setIsNotificationModalOpen(false);
    setNewNotification({ name: '', parameters: '' });
  };

  const handleSaveCommand = () => {
    // In a real app, you would call an API here
    alert(`Command sent: ${newCommand.name}`);
    setIsCommandModalOpen(false);
    setNewCommand({ name: '', parameters: '' });
  };

  const renderDashboard = () => (
    <div className="grid grid-cols-2 gap-6">
      <div className="bg-white rounded-md shadow-sm p-6 flex flex-col items-center">
        <div className="text-4xl text-sems-primary mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"/>
            <path d="M16 12h-6"/>
            <path d="M12 16V8"/>
          </svg>
        </div>
        <h2 className="text-xl font-medium mb-2">Dashboard</h2>
        <p className="text-gray-600 mb-4">View device status and metrics</p>
        <Button
          className="w-full bg-sems-primary hover:bg-sems-secondary text-white"
          onClick={() => setActiveTab('dashboard')}
        >
          View Dashboard
        </Button>
      </div>

      <div className="bg-white rounded-md shadow-sm p-6 flex flex-col items-center">
        <div className="text-4xl text-sems-primary mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/>
            <path d="m15 5 4 4"/>
          </svg>
        </div>
        <h2 className="text-xl font-medium mb-2">Edit Device</h2>
        <p className="text-gray-600 mb-4">Modify device configuration</p>
        <Button
          className="w-full bg-sems-primary hover:bg-sems-secondary text-white"
          onClick={() => setActiveTab('edit')}
        >
          Edit Device
        </Button>
      </div>

      <div className="bg-white rounded-md shadow-sm p-6 flex flex-col items-center">
        <div className="text-4xl text-sems-primary mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M3 18h18v-2H3v2zM3 13h18v-2H3v2zM3 6v2h18V6H3z"/>
          </svg>
        </div>
        <h2 className="text-xl font-medium mb-2">Commands</h2>
        <p className="text-gray-600 mb-4">Send and view device commands</p>
        <Button
          className="w-full bg-sems-primary hover:bg-sems-secondary text-white"
          onClick={() => setActiveTab('commands')}
        >
          View Commands
        </Button>
      </div>

      <div className="bg-white rounded-md shadow-sm p-6 flex flex-col items-center">
        <div className="text-4xl text-sems-primary mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/>
            <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"/>
          </svg>
        </div>
        <h2 className="text-xl font-medium mb-2">Notifications</h2>
        <p className="text-gray-600 mb-4">View device notifications</p>
        <Button
          className="w-full bg-sems-primary hover:bg-sems-secondary text-white"
          onClick={() => setActiveTab('notifications')}
        >
          View Notifications
        </Button>
      </div>
    </div>
  );

  const renderEdit = () => (
    <div className="bg-white rounded-md shadow-sm p-6">
      <h2 className="text-xl font-medium mb-6">Device Info</h2>
      <div className="space-y-4">
        <div>
          <label className="block mb-2">Name</label>
          <Input className="w-full" defaultValue={`Device ${deviceId}`} />
        </div>
        <div>
          <label className="block mb-2">Network</label>
          <select className="w-full p-2 border rounded">
            <option>Select Network</option>
            <option>Home Network</option>
            <option>Office Network</option>
            <option>IoT Network</option>
          </select>
        </div>
        <div>
          <label className="block mb-2">Device Type</label>
          <select className="w-full p-2 border rounded">
            <option>Select Device Type</option>
            <option>Smart Thermostat</option>
            <option>Smart Light Bulb</option>
            <option>Smart Lock</option>
          </select>
        </div>
        <div>
          <label className="block mb-2">Operation</label>
          <select className="w-full p-2 border rounded">
            <option>Operational</option>
            <option>Blocked</option>
            <option>Maintenance</option>
          </select>
        </div>
        <div>
          <label className="block mb-2">Data</label>
          <Textarea className="w-full h-32" defaultValue="{}" />
        </div>
        <div className="flex justify-end gap-2">
          <Button
            className="bg-red-500 hover:bg-red-600 text-white"
            onClick={() => navigate('/admin/devices')}
          >
            Cancel
          </Button>
          <Button
            className="bg-sems-primary hover:bg-sems-secondary text-white"
            onClick={() => {
              alert('Device updated');
              navigate('/admin/devices');
            }}
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );

  const renderCommands = () => (
    <div className="bg-white rounded-md shadow-sm">
      <div className="p-4 border-b flex justify-between items-center">
        <h2 className="text-lg font-medium">Commands</h2>
        <Button
          className="bg-sems-primary hover:bg-sems-secondary text-white"
          onClick={handleSendCommand}
        >
          Send Command
        </Button>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 text-left">
            <tr>
              <th className="p-4 font-medium">Name</th>
              <th className="p-4 font-medium">Timestamp</th>
              <th className="p-4 font-medium">Parameters</th>
              <th className="p-4 font-medium">Status</th>
              <th className="p-4 font-medium">Result</th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {sampleCommands.map((command) => (
              <tr key={command.id} className="hover:bg-gray-50">
                <td className="p-4">{command.name}</td>
                <td className="p-4">{new Date(command.timestamp).toISOString()}</td>
                <td className="p-4">{command.parameters}</td>
                <td className="p-4">
                  <span
                    className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                      command.status === 'completed'
                        ? 'bg-green-100 text-green-800'
                        : command.status === 'in progress'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {command.status}
                  </span>
                </td>
                <td className="p-4">{command.result}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderNotifications = () => (
    <div className="bg-white rounded-md shadow-sm">
      <div className="p-4 border-b flex justify-between items-center">
        <h2 className="text-lg font-medium">Notifications</h2>
        <Button
          className="bg-sems-primary hover:bg-sems-secondary text-white"
          onClick={handleSendNotification}
        >
          Send Notification
        </Button>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 text-left">
            <tr>
              <th className="p-4 font-medium">Name</th>
              <th className="p-4 font-medium">Timestamp</th>
              <th className="p-4 font-medium">Parameters</th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {sampleNotifications.map((notification) => (
              <tr key={notification.id} className="hover:bg-gray-50">
                <td className="p-4">{notification.name}</td>
                <td className="p-4">{new Date(notification.timestamp).toISOString()}</td>
                <td className="p-4">{notification.parameters}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <AdminLayout>
      {activeTab === 'dashboard' && renderDashboard()}
      {activeTab === 'edit' && renderEdit()}
      {activeTab === 'commands' && renderCommands()}
      {activeTab === 'notifications' && renderNotifications()}

      {/* Notification Modal */}
      <Modal
        title="Create New Notification"
        isOpen={isNotificationModalOpen}
        onClose={() => setIsNotificationModalOpen(false)}
        onSave={handleSaveNotification}
        onCancel={() => setIsNotificationModalOpen(false)}
      >
        <div className="space-y-4">
          <div>
            <label className="block mb-2">Name</label>
            <Input
              placeholder="Enter name"
              value={newNotification.name}
              onChange={(e) => setNewNotification({ ...newNotification, name: e.target.value })}
            />
          </div>
          <div>
            <label className="block mb-2">Parameters (JSON)</label>
            <Textarea
              placeholder="Enter parameters"
              value={newNotification.parameters}
              onChange={(e) => setNewNotification({ ...newNotification, parameters: e.target.value })}
              className="h-32"
            />
          </div>
        </div>
      </Modal>

      {/* Command Modal */}
      <Modal
        title="Create New Command"
        isOpen={isCommandModalOpen}
        onClose={() => setIsCommandModalOpen(false)}
        onSave={handleSaveCommand}
        onCancel={() => setIsCommandModalOpen(false)}
      >
        <div className="space-y-4">
          <div>
            <label className="block mb-2">Name</label>
            <Input
              placeholder="Enter name"
              value={newCommand.name}
              onChange={(e) => setNewCommand({ ...newCommand, name: e.target.value })}
            />
          </div>
          <div>
            <label className="block mb-2">Parameters (JSON)</label>
            <Textarea
              placeholder="Enter parameters"
              value={newCommand.parameters}
              onChange={(e) => setNewCommand({ ...newCommand, parameters: e.target.value })}
              className="h-32"
            />
          </div>
        </div>
      </Modal>
    </AdminLayout>
  );
};

export default DeviceDetail;
