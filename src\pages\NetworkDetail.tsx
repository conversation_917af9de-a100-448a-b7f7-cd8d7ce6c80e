import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import FormInput from '@/components/FormInput';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/sonner';
import { getNetworkById, updateNetwork, deleteNetwork, NetworkResponse } from '@/utils/networkService';

const NetworkDetail = () => {
  const { networkId } = useParams<{ networkId: string }>();
  const navigate = useNavigate();
  const [network, setNetwork] = useState<NetworkResponse | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNetwork = async () => {
      try {
        if (!networkId) return;
        
        setLoading(true);
        const networkData = await getNetworkById(parseInt(networkId));
        console.log('Network fetched:', networkData);
        setNetwork(networkData);
        setFormData({
          name: networkData.name,
          description: networkData.description,
        });
      } catch (err) {
        console.error('Error fetching network:', err);
        setError('Failed to load network details');
        toast.error('Failed to load network details');
      } finally {
        setLoading(false);
      }
    };

    fetchNetwork();
  }, [networkId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (!networkId || !network) return;
      
      setIsSubmitting(true);
      
      // Appel à l'API pour mettre à jour le réseau
      await updateNetwork(parseInt(networkId), {
        name: formData.name,
        description: formData.description
      });
      
      toast.success('Network updated successfully');
      navigate('/admin/networks');
    } catch (error) {
      console.error('Error updating network:', error);
      toast.error('Failed to update network');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!networkId || !network) return;
    
    if (window.confirm(`Are you sure you want to delete network ${network.name}?`)) {
      try {
        await deleteNetwork(parseInt(networkId));
        toast.success('Network deleted successfully');
        navigate('/admin/networks');
      } catch (err) {
        console.error('Error deleting network:', err);
        toast.error('Failed to delete network');
      }
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <p>Loading network details...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error || !network) {
    return (
      <AdminLayout>
        <div className="bg-white rounded-md shadow-sm p-6">
          <h2 className="text-xl font-medium mb-6 text-red-500">{error || 'Network not found'}</h2>
          <Button onClick={() => navigate('/admin/networks')}>Back to Networks</Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-medium">Edit Network</h2>
          <Button onClick={handleDelete} className="bg-red-500 hover:bg-red-600 text-white">
            Delete
          </Button>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <FormInput
            label="Name"
            id="name"
            placeholder="Enter network name"
            value={formData.name}
            onChange={(e) => handleChange(e)}
            name="name"
            required
          />
          
          <div>
            <label className="block mb-2">Description</label>
            <Textarea
              className="w-full h-32"
              placeholder="Enter network description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
            />
          </div>
          
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              className="bg-gray-500 hover:bg-gray-600 text-white"
              onClick={() => navigate('/admin/networks')}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-sems-primary hover:bg-sems-secondary text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default NetworkDetail;