import AsyncStorage from '@react-native-async-storage/async-storage';

// Configuration de l'API
const API_CONFIG = {
  // En développement, utilisez l'IP de votre machine locale
  // Remplacez par l'IP de votre serveur de développement
  BASE_URL: 'http://**************:80', // Ajustez selon votre configuration
  TIMEOUT: 10000, // 10 secondes
};

// Interface pour les options de requête
interface ApiRequestOptions extends RequestInit {
  timeout?: number;
}

// Fonction utilitaire pour créer un timeout
function createTimeoutPromise(timeout: number): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Request timeout')), timeout);
  });
}

// Fonction principale pour les requêtes API
export async function apiRequest<T>(
  endpoint: string,
  options: ApiRequestOptions = {}
): Promise<T> {
  try {
    // Récupérer le token d'authentification depuis AsyncStorage
    const token = await AsyncStorage.getItem('sems-token');
    
    // Construire l'URL complète
    const url = endpoint.startsWith('http') 
      ? endpoint 
      : `${API_CONFIG.BASE_URL}${endpoint}`;
    
    // Préparer les en-têtes avec le token d'authentification
    const headers = {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
      ...options.headers,
    };

    // Configuration de la requête
    const requestConfig: RequestInit = {
      ...options,
      headers,
    };

    // Timeout par défaut
    const timeout = options.timeout || API_CONFIG.TIMEOUT;

    // Exécuter la requête avec timeout
    const response = await Promise.race([
      fetch(url, requestConfig),
      createTimeoutPromise(timeout)
    ]);

    // Vérifier le statut de la réponse
    if (!response.ok) {
      let errorMessage = `HTTP Error: ${response.status}`;
      
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch {
        // Si on ne peut pas parser le JSON d'erreur, utiliser le message par défaut
      }
      
      const error = new Error(errorMessage);
      (error as any).status = response.status;
      throw error;
    }
    
    // Analyser la réponse JSON
    const data = await response.json();
    return data as T;
  } catch (error: any) {
    console.error('API Request Error:', error);
    
    // Amélioration des messages d'erreur
    if (error.name === 'AbortError' || error.message === 'Request timeout') {
      throw new Error('Request timeout: The server took too long to respond');
    } else if (error.message.includes('Network request failed') || error.message.includes('fetch')) {
      throw new Error('Network error: Unable to connect to the server. Please check your internet connection.');
    }
    
    throw error;
  }
}

// Fonction utilitaire pour sauvegarder le token
export async function saveAuthToken(token: string): Promise<void> {
  try {
    await AsyncStorage.setItem('sems-token', token);
  } catch (error) {
    console.error('Error saving auth token:', error);
    throw new Error('Failed to save authentication token');
  }
}

// Fonction utilitaire pour récupérer le token
export async function getAuthToken(): Promise<string | null> {
  try {
    return await AsyncStorage.getItem('sems-token');
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
}

// Fonction utilitaire pour supprimer le token
export async function removeAuthToken(): Promise<void> {
  try {
    await AsyncStorage.removeItem('sems-token');
  } catch (error) {
    console.error('Error removing auth token:', error);
    throw new Error('Failed to remove authentication token');
  }
}

// Fonction pour vérifier la connectivité réseau (optionnelle)
export async function checkNetworkConnectivity(): Promise<boolean> {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/health`, {
      method: 'HEAD',
      timeout: 5000,
    });
    return response.ok;
  } catch {
    return false;
  }
}
