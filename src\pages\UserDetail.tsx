import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/sonner';
import { getUserById, UserResponse, deleteUser } from '@/utils/userService';

const UserDetail = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const [user, setUser] = useState<UserResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        if (!userId) return;
        
        setLoading(true);
        const userData = await getUserById(parseInt(userId));
        console.log('User fetched:', userData);
        setUser(userData);
      } catch (err) {
        console.error('Error fetching user:', err);
        setError('Failed to load user details');
        toast.error('Failed to load user details');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  const handleDelete = async () => {
    if (!user) return;
    
    if (window.confirm(`Are you sure you want to delete user ${user.login}?`)) {
      try {
        await deleteUser(user.id);
        toast.success('User deleted successfully');
        navigate('/admin/users');
      } catch (err) {
        console.error('Error deleting user:', err);
        toast.error('Failed to delete user');
      }
    }
  };

  const handleEdit = () => {
    navigate(`/admin/users/edit/${userId}`);
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <p>Loading user details...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error || !user) {
    return (
      <AdminLayout>
        <div className="bg-white rounded-md shadow-sm p-6">
          <h2 className="text-xl font-medium mb-6 text-red-500">{error || 'User not found'}</h2>
          <Button onClick={() => navigate('/admin/users')}>Back to Users</Button>
        </div>
      </AdminLayout>
    );
  }

  // Parse the JSON string from user.data.jsonString
  let userData = {};
  try {
    if (user.data && user.data.jsonString) {
      userData = JSON.parse(user.data.jsonString);
    }
  } catch (err) {
    console.error('Error parsing user data:', err);
  }

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-medium">User Details</h2>
          <div className="flex gap-2">
            <Button onClick={handleEdit} className="bg-blue-500 hover:bg-blue-600 text-white">
              Edit
            </Button>
            <Button onClick={handleDelete} className="bg-red-500 hover:bg-red-600 text-white">
              Delete
            </Button>
          </div>
        </div>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-medium">ID</h3>
            <p>{user.id}</p>
          </div>
          
          <div>
            <h3 className="font-medium">Login</h3>
            <p>{user.login}</p>
          </div>
          
          <div>
            <h3 className="font-medium">Role</h3>
            <p>{user.role === 1 ? 'Administrator' : 'Client'}</p>
          </div>
          
          <div>
            <h3 className="font-medium">Status</h3>
            <p>
              {user.status === 0 ? 'Active' : 
               user.status === 1 ? 'Locked' : 'Disabled'}
            </p>
          </div>
          
          <div>
            <h3 className="font-medium">Last Login</h3>
            <p>{user.lastLogin ? new Date(user.lastLogin).toLocaleString() : 'Never'}</p>
          </div>
          
          <div>
            <h3 className="font-medium">Data</h3>
            <pre className="bg-gray-100 p-3 rounded overflow-auto max-h-60">
              {JSON.stringify(userData, null, 2) || 'No data'}
            </pre>
          </div>
        </div>
        
        <div className="mt-6">
          <Button onClick={() => navigate('/admin/users')} className="bg-gray-500 hover:bg-gray-600 text-white">
            Back to Users
          </Button>
        </div>
      </div>
    </AdminLayout>
  );
};

export default UserDetail;


