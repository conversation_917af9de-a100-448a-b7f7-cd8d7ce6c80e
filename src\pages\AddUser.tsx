import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import FormInput from '@/components/FormInput';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { createUser, UserData } from '@/utils/userService';
import { toast } from '@/components/ui/sonner';

const AddUser = () => {
  const [formData, setFormData] = useState({
    login: '',
    role: '1', // Default to Administrator (1)
    status: '0', // Default to Active (0)
    password: '',
    confirmPassword: '',
    data: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const navigate = useNavigate();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    try {
      console.log('Preparing user data for submission...');
      
      // Préparer les données utilisateur selon le format attendu par l'API
      const userData: UserData = {
        login: formData.login,
        role: parseInt(formData.role),
        status: parseInt(formData.status),
        password: formData.password,
        // Formater correctement les données JSON
        data: formData.data ? { 
          jsonString: formData.data 
        } : undefined,
        introReviewed: false // Valeur par défaut
      };

      console.log('Submitting user data:', { ...userData, password: '***' });
      
      // Ajout d'un timeout pour éviter que la requête ne reste bloquée trop longtemps
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 secondes de timeout
      
      try {
        await createUser(userData);
        clearTimeout(timeoutId);
        
        toast.success('User created successfully!');
        navigate('/admin/users');
      } catch (fetchError: any) {
        clearTimeout(timeoutId);
        throw fetchError;
      }
    } catch (err: any) {
      console.error('Error creating user:', err);
      
      // Messages d'erreur plus spécifiques
      if (err.name === 'AbortError') {
        setError('Request timeout: The server took too long to respond');
      } else if (err.message.includes('Failed to fetch')) {
        setError('Network error: Unable to connect to the server. Please check your network connection.');
      } else {
        setError(err.message || 'An error occurred while creating the user');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6">
        <h2 className="text-xl font-medium mb-6">Create New User</h2>
        {error && <div className="text-red-500 mb-4 p-3 bg-red-50 rounded border border-red-200">{error}</div>}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <FormInput
            label="Login"
            id="login"
            name="login"
            placeholder="Enter login name"
            value={formData.login}
            onChange={handleChange}
            required
          />
          
          <div>
            <label className="block mb-2">Role</label>
            <select
              className="w-full p-2 border rounded"
              name="role"
              value={formData.role}
              onChange={handleChange}
            >
              <option value="1">Administrator</option>
              <option value="0">Client</option>
            </select>
          </div>
          
          <div>
            <label className="block mb-2">Status</label>
            <select
              className="w-full p-2 border rounded"
              name="status"
              value={formData.status}
              onChange={handleChange}
            >
              <option value="0">Active</option>
              <option value="1">Locked</option>
              <option value="2">Disabled</option>
            </select>
          </div>
          
          <FormInput
            label="Password"
            id="password"
            name="password"
            type="password"
            placeholder="Enter password"
            value={formData.password}
            onChange={handleChange}
            required
          />
          
          <FormInput
            label="Confirm Password"
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            placeholder="Confirm password"
            value={formData.confirmPassword}
            onChange={handleChange}
            required
          />
          
          <div>
            <label className="block mb-2">Data</label>
            <Textarea
              className="w-full h-32"
              placeholder='Enter data (e.g., {"email":"<EMAIL>","phone":"************"})'
              name="data"
              value={formData.data}
              onChange={handleChange}
            />
          </div>
          
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              className="bg-red-500 hover:bg-red-600 text-white"
              onClick={() => navigate('/admin/users')}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-sems-primary hover:bg-sems-secondary text-white"
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default AddUser;











