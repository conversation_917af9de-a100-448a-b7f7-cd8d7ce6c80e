
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Input } from '@/components/ui/input';
import TableActions from '@/components/TableActions';

// Sample data
const initialDeviceTypes = [
  { id: 1, name: 'Smart Thermostat', description: 'A device that controls the temperature of a building automatically.' },
  { id: 2, name: 'Smart Light Bulb', description: 'A light bulb that can be controlled remotely and can change color.' },
  { id: 3, name: 'Smart Lock', description: 'A lock that can be controlled remotely and can provide keyless entry.' },
  { id: 4, name: 'Security Camera', description: 'A camera that monitors and records activity in a specific area.' },
  { id: 5, name: 'Smart Speaker', description: 'A speaker that can respond to voice commands and control other smart devices.' },
  { id: 6, name: 'Smart Plug', description: 'A plug that can be controlled remotely to turn devices on and off.' },
  { id: 7, name: 'Smart Smoke Detector', description: 'A smoke detector that can send alerts to your phone when it detects smoke.' },
  { id: 8, name: 'Smart Doorbell', description: 'A doorbell with a camera and microphone that allows you to see and talk to visitors.' },
  { id: 9, name: 'Smart Refrigerator', description: 'A refrigerator that can monitor and manage its contents and energy usage.' },
  { id: 10, name: 'Smart Air Purifier', description: 'An air purifier that can monitor and improve air quality in real-time.' },
];

const DeviceTypes = () => {
  const [deviceTypes] = useState(initialDeviceTypes);
  const [searchTerm, setSearchTerm] = useState('');
  const navigate = useNavigate();

  const filteredDeviceTypes = deviceTypes.filter(deviceType =>
    deviceType.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddDeviceType = () => {
    navigate('/admin/deviceTypes/add');
  };

  const handleEditDeviceType = (deviceTypeId: number) => {
    navigate(`/admin/deviceTypes/${deviceTypeId}`);
  };

  const handleDeleteDeviceType = (deviceTypeId: number) => {
    // In a real app, you would call an API here
    alert(`Delete device type with ID ${deviceTypeId}`);
  };

  return (
    <AdminLayout
      actionButton={{
        label: 'Add New Device Type',
        onClick: handleAddDeviceType,
      }}
    >
      <div className="bg-white rounded-md shadow-sm">
        <div className="p-4 border-b">
          <div className="flex items-center gap-2">
            <span>Search By Name:</span>
            <Input
              placeholder="Enter device type name"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 text-left">
              <tr>
                <th className="p-4 font-medium">Name</th>
                <th className="p-4 font-medium">Description</th>
                <th className="p-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y">
              {filteredDeviceTypes.map((deviceType) => (
                <tr key={deviceType.id} className="hover:bg-gray-50">
                  <td className="p-4">{deviceType.name}</td>
                  <td className="p-4">{deviceType.description}</td>
                  <td className="p-4">
                    <TableActions 
                      onEdit={() => handleEditDeviceType(deviceType.id)}
                      onDelete={() => handleDeleteDeviceType(deviceType.id)}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  );
};

export default DeviceTypes;
