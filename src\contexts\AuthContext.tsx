
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserResponse, getCurrentUser } from '@/utils/userService';
import { authenticate, TokenResponse } from '@/utils/tokenService';

interface AuthContextType {
  isAuthenticated: boolean;
  token: string | null;
  currentUser: UserResponse | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  refreshCurrentUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Modification: Séparation de la fonction useAuth pour la compatibilité HMR
function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Modification: Export des fonctions séparément pour la compatibilité HMR
export { useAuth };

export function AuthProvider({ children }: { children: ReactNode }) {
  const [token, setToken] = useState<string | null>(localStorage.getItem('sems-token'));
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(!!token);
  const [currentUser, setCurrentUser] = useState<UserResponse | null>(null);
  const navigate = useNavigate();

  // Fonction pour récupérer l'utilisateur actuel
  const refreshCurrentUser = async () => {
    if (token) {
      try {
        const user = await getCurrentUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('Failed to fetch current user:', error);
        // Si l'erreur est due à un token invalide, déconnectez l'utilisateur
        if (error instanceof Error && error.message.includes('401')) {
          logout();
        }
      }
    }
  };

  useEffect(() => {
    // Check if token exists in localStorage
    const storedToken = localStorage.getItem('sems-token');
    if (storedToken) {
      setToken(storedToken);
      setIsAuthenticated(true);
      // Récupérer les informations de l'utilisateur actuel
      refreshCurrentUser();
    }
  }, []);

  const login = async (username: string, password: string) => {
    try {
      console.log('Attempting login for:', username);
      
      // Utiliser le service d'authentification
      const authResponse = await authenticate({ 
        login: username, 
        password 
      });
      
      console.log('Login successful, token received');
      
      // Extraire le token de la réponse
      const jwt = authResponse.accessToken;
      
      // Stockez le token dans localStorage
      localStorage.setItem('sems-token', jwt);
      
      // Mettez à jour l'état
      setToken(jwt);
      setIsAuthenticated(true);
      
      // Récupérer les informations de l'utilisateur actuel
      await refreshCurrentUser();
      
      // Naviguez vers le tableau de bord admin
      navigate('/admin');
    } catch (error) {
      console.error('Login error:', error);
      throw new Error('Authentication failed. Please check your credentials.');
    }
  };

  const logout = () => {
    // Remove token from localStorage
    localStorage.removeItem('sems-token');
    
    // Update state
    setToken(null);
    setIsAuthenticated(false);
    setCurrentUser(null);
    
    // Navigate to login page
    navigate('/login');
  };

  return (
    <AuthContext.Provider value={{ 
      isAuthenticated, 
      token, 
      currentUser, 
      login, 
      logout, 
      refreshCurrentUser 
    }}>
      {children}
    </AuthContext.Provider>
  );
}
























