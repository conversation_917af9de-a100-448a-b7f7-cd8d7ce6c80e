
import { useState, useEffect } from 'react';
import { Users, Network, Cpu, Layers, Plug } from 'lucide-react';
import AdminLayout from '@/layouts/AdminLayout';
import DashboardCard from '@/components/DashboardCard';
import { getUserCount } from '@/utils/userService';
import { getNetworkCount } from '@/utils/networkService';
import { toast } from '@/components/ui/sonner';

const Dashboard = () => {
  const [stats, setStats] = useState({
    users: 0,
    networks: 0,
    devices: 0,
    deviceTypes: 0,
    plugins: 0,
  });
  const [loading, setLoading] = useState(true);
  // Suppression de l'état showDebugger

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        
        // Fetch real data where available
        const [userCount, networkCount] = await Promise.all([
          getUserCount(),
          getNetworkCount(),
        ]);
        
        setStats({
          users: userCount,
          networks: networkCount,
          // Placeholder data for now
          devices: 12,
          deviceTypes: 5,
          plugins: 3,
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        toast.error('Failed to load dashboard statistics');
      } finally {
        setLoading(false);
      }
    };
    
    fetchStats();
  }, []);

  return (
    <AdminLayout>
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Dashboard</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <DashboardCard
            title="Users"
            value={stats.users}
            icon={<Users className="h-5 w-5" />}
            loading={loading}
          />
          <DashboardCard
            title="Networks"
            value={stats.networks}
            icon={<Network className="h-5 w-5" />}
            loading={loading}
          />
          <DashboardCard
            title="Devices"
            value={stats.devices}
            icon={<Cpu className="h-5 w-5" />}
            loading={loading}
          />
          <DashboardCard
            title="Device Types"
            value={stats.deviceTypes}
            icon={<Layers className="h-5 w-5" />}
            loading={loading}
          />
          <DashboardCard
            title="Plugins"
            value={stats.plugins}
            icon={<Plug className="h-5 w-5" />}
            loading={loading}
          />
        </div>
      </div>
    </AdminLayout>
  );
};

export default Dashboard;





