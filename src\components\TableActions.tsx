
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';

interface TableActionsProps {
  editUrl?: string;
  onEdit?: () => void;
  onDelete?: () => void;
  showDetails?: boolean;
  detailsUrl?: string;
  onDetails?: () => void;
}

const TableActions = ({ 
  editUrl, 
  onEdit, 
  onDelete, 
  showDetails = false,
  detailsUrl,
  onDetails
}: TableActionsProps) => {
  return (
    <div className="flex gap-2">
      {showDetails && (
        detailsUrl ? (
          <Link to={detailsUrl}>
            <Button 
              className="bg-blue-500 hover:bg-blue-600 text-white px-4"
              size="sm"
            >
              Details
            </Button>
          </Link>
        ) : (
          <Button 
            className="bg-blue-500 hover:bg-blue-600 text-white px-4"
            onClick={onDetails}
            size="sm"
          >
            Details
          </Button>
        )
      )}
      
      {editUrl ? (
        <Link to={editUrl}>
          <Button 
            className="bg-blue-500 hover:bg-blue-600 text-white px-4"
            size="sm"
          >
            Edit
          </Button>
        </Link>
      ) : (
        <Button 
          className="bg-blue-500 hover:bg-blue-600 text-white px-4"
          onClick={onEdit}
          size="sm"
        >
          Edit
        </Button>
      )}
      
      <Button 
        className="bg-red-500 hover:bg-red-600 text-white px-4"
        onClick={onDelete}
        size="sm"
      >
        Delete
      </Button>
    </div>
  );
};

export default TableActions;
