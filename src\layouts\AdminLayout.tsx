
import React from 'react';
import Sidebar from '@/components/Sidebar';
import Header from '@/components/Header';

interface ActionButtonProps {
  label: string;
  onClick: () => void;
}

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
  actionButton?: ActionButtonProps;
}

const AdminLayout = (props: AdminLayoutProps) => {
  const { children, title, actionButton } = props;
  
  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header title={title} actionButton={actionButton} />
        <main className="flex-1 overflow-y-auto p-6">
          {children}
        </main>
        <footer className="bg-gray-900 text-white text-center text-xs py-2">
          Copyright © {new Date().getFullYear()} | Smart Waves Technologies
        </footer>
      </div>
    </div>
  );
};

export default AdminLayout;

