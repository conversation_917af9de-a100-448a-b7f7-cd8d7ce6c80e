
import React from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';

interface ActionButtonProps {
  label: string;
  onClick: () => void;
}

interface HeaderProps {
  title?: string;
  actionButton?: ActionButtonProps;
}

const Header = ({ title = "Dashboard", actionButton }: HeaderProps) => {
  const { logout } = useAuth();

  return (
    <header className="bg-white border-b border-gray-200 py-4 px-6 flex items-center justify-between">
      <h1 className="text-xl font-semibold text-gray-800">{title}</h1>
      
      <div className="flex items-center gap-4">
        {actionButton && (
          <Button 
            onClick={actionButton.onClick}
            className="bg-sems-primary hover:bg-sems-secondary text-white"
          >
            {actionButton.label}
          </Button>
        )}
        
        <Button 
          variant="outline" 
          onClick={logout}
          className="border-gray-300 text-gray-700 hover:bg-gray-100"
        >
          Logout
        </Button>
      </div>
    </header>
  );
};

export default Header;

