
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/sonner';
import { getAllUsers, UserResponse, deleteUser } from '@/utils/userService';

const Users = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [users, setUsers] = useState<UserResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const userData = await getAllUsers();
        setUsers(userData);
      } catch (error) {
        console.error('Error fetching users:', error);
        toast.error('Failed to load users');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  const filteredUsers = users.filter(user =>
    user.login.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddUser = () => {
    navigate('/admin/users/add');
  };

  const handleUserDetails = (userId: number) => {
    navigate(`/admin/users/${userId}`);
  };

  const handleDeleteUser = async (userId: number, login: string) => {
    if (window.confirm(`Are you sure you want to delete user ${login}?`)) {
      try {
        await deleteUser(userId);
        // Mettre à jour la liste des utilisateurs après la suppression
        setUsers(users.filter(user => user.id !== userId));
        toast.success('User deleted successfully');
      } catch (error) {
        console.error('Error deleting user:', error);
        toast.error('Failed to delete user');
      }
    }
  };

  return (
    <AdminLayout
      actionButton={{
        label: 'Add New User',
        onClick: handleAddUser,
      }}
    >
      <div className="bg-white rounded-md shadow-sm">
        <div className="p-4 border-b">
          <div className="flex items-center gap-2">
            <span>Search By Login:</span>
            <Input
              placeholder="Enter login name"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 text-left">
              <tr>
                <th className="p-4 font-medium">Login</th>
                <th className="p-4 font-medium">Role</th>
                <th className="p-4 font-medium">Status</th>
                <th className="p-4 font-medium">LastLogin</th>
                <th className="p-4 font-medium">Data</th>
                <th className="p-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y">
              {loading ? (
                <tr>
                  <td colSpan={6} className="p-4 text-center">Loading users...</td>
                </tr>
              ) : filteredUsers.length === 0 ? (
                <tr>
                  <td colSpan={6} className="p-4 text-center">No users found</td>
                </tr>
              ) : (
                filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="p-4">{user.login}</td>
                    <td className="p-4">{user.role === 1 ? 'Administrator' : 'Client'}</td>
                    <td className="p-4">
                      <span
                        className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                          user.status === 0 
                            ? 'bg-green-100 text-green-800' 
                            : user.status === 1 
                            ? 'bg-orange-100 text-orange-800' 
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {user.status === 0 ? 'Active' : user.status === 1 ? 'Locked' : 'Disabled'}
                      </span>
                    </td>
                    <td className="p-4">{new Date(user.lastLogin).toLocaleString()}</td>
                    <td className="p-4 max-w-xs truncate" title={user.data?.jsonString || ''}>
                      {user.data?.jsonString ? JSON.stringify(JSON.parse(user.data.jsonString)) : ''}
                    </td>
                    <td className="p-4">
                      <div className="flex gap-2">
                        <Button 
                          className="bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 h-auto"
                          onClick={() => handleUserDetails(user.id)}
                        >
                          Details
                        </Button>
                        <Button 
                          className="bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 h-auto"
                          onClick={() => handleDeleteUser(user.id, user.login)}
                        >
                          Delete
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Users;

























