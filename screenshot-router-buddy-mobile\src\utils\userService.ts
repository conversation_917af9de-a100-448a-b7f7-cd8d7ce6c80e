import { apiRequest } from './api';

// Interface pour les données utilisateur
export interface UserData {
  login: string;
  role: number;
  status: number;
  password?: string;
  data?: {
    jsonString: string;
  };
  introReviewed?: boolean;
}

// Interface pour la réponse de l'API utilisateur
export interface UserResponse {
  id: number;
  login: string;
  role: number;
  status: number;
  lastLogin: string;
  data: {
    jsonString: string;
  };
  introReviewed: boolean;
}

// Fonction pour créer un utilisateur
export async function createUser(userData: UserData): Promise<UserResponse> {
  try {
    console.log('Creating user with data:', { ...userData, password: '***' });
    
    const response = await apiRequest<UserResponse>('/api/rest/user', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
    
    console.log('User created successfully:', response);
    return response;
  } catch (error: any) {
    console.error('Error in createUser service:', error);
    
    // Amélioration des messages d'erreur
    if (error.message.includes('Network error')) {
      throw new Error('Network error: Unable to connect to the server');
    } else if (error.status === 400) {
      throw new Error('Invalid user data: ' + (error.message || 'Please check your input'));
    } else if (error.status === 401 || error.status === 403) {
      throw new Error('Authentication error: You do not have permission to create users');
    } else if (error.status === 409) {
      throw new Error('User with this login already exists');
    }
    
    throw error;
  }
}

// Fonction pour récupérer un utilisateur par son ID
export async function getUserById(userId: number): Promise<UserResponse> {
  try {
    return await apiRequest<UserResponse>(`/api/rest/user/${userId}`);
  } catch (error: any) {
    console.error('Error getting user by ID:', error);
    
    if (error.status === 404) {
      throw new Error('User not found');
    } else if (error.status === 401) {
      throw new Error('Authentication required. Please log in again.');
    }
    
    throw new Error('Failed to retrieve user information');
  }
}

// Fonction pour récupérer tous les utilisateurs
export async function getAllUsers(): Promise<UserResponse[]> {
  try {
    return await apiRequest<UserResponse[]>('/api/rest/user');
  } catch (error: any) {
    console.error('Error getting all users:', error);
    
    if (error.status === 401) {
      throw new Error('Authentication required. Please log in again.');
    } else if (error.status === 403) {
      throw new Error('Insufficient permissions to view users');
    }
    
    throw new Error('Failed to retrieve users list');
  }
}

// Fonction pour mettre à jour un utilisateur
export async function updateUser(userId: number, userData: Partial<UserData>): Promise<UserResponse> {
  try {
    console.log('Updating user:', userId, { ...userData, password: userData.password ? '***' : undefined });
    
    return await apiRequest<UserResponse>(`/api/rest/user/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  } catch (error: any) {
    console.error('Error updating user:', error);
    
    if (error.status === 404) {
      throw new Error('User not found');
    } else if (error.status === 401) {
      throw new Error('Authentication required. Please log in again.');
    } else if (error.status === 403) {
      throw new Error('Insufficient permissions to update this user');
    } else if (error.status === 409) {
      throw new Error('User with this login already exists');
    }
    
    throw new Error('Failed to update user');
  }
}

// Fonction pour supprimer un utilisateur
export async function deleteUser(userId: number): Promise<void> {
  try {
    await apiRequest<void>(`/api/rest/user/${userId}`, {
      method: 'DELETE',
    });
  } catch (error: any) {
    console.error('Error deleting user:', error);
    
    if (error.status === 404) {
      throw new Error('User not found');
    } else if (error.status === 401) {
      throw new Error('Authentication required. Please log in again.');
    } else if (error.status === 403) {
      throw new Error('Insufficient permissions to delete this user');
    }
    
    throw new Error('Failed to delete user');
  }
}

// Fonction pour obtenir le nombre total d'utilisateurs
export async function getUserCount(): Promise<number> {
  try {
    // Si l'API ne fournit pas d'endpoint spécifique pour le comptage
    const users = await getAllUsers();
    return users.length;
  } catch (error) {
    console.error('Error getting user count:', error);
    throw new Error('Failed to get user count');
  }
}

// Fonction pour récupérer l'utilisateur actuellement connecté
export async function getCurrentUser(): Promise<UserResponse> {
  try {
    // Si l'API a un endpoint spécifique pour l'utilisateur actuel
    return await apiRequest<UserResponse>('/api/rest/user/current');
  } catch (error: any) {
    console.error('Error getting current user:', error);
    
    if (error.status === 401) {
      throw new Error('Authentication required. Please log in again.');
    }
    
    throw new Error('Failed to retrieve current user information');
  }
}
