import { apiRequest } from './api';

// Interface pour les données de réseau
export interface NetworkData {
  name: string;
  description: string;
}

// Interface pour la réponse de l'API réseau
export interface NetworkResponse {
  id: number;
  name: string;
  description: string;
}

// Interface pour la réponse du comptage de réseaux
export interface CountResponse {
  count: number;
}

// Fonction pour créer un réseau
export async function createNetwork(networkData: NetworkData): Promise<NetworkResponse> {
  return apiRequest<NetworkResponse>('/network', {
    method: 'POST',
    body: JSON.stringify(networkData),
  });
}

// Fonction pour récupérer un réseau par son ID
export async function getNetworkById(networkId: number): Promise<NetworkResponse> {
  return apiRequest<NetworkResponse>(`/network/${networkId}`);
}

// Fonction pour récupérer tous les réseaux
export async function getAllNetworks(): Promise<NetworkResponse[]> {
  return apiRequest<NetworkResponse[]>('/network');
}

// Fonction pour mettre à jour un réseau
export async function updateNetwork(networkId: number, networkData: Partial<NetworkData>): Promise<NetworkResponse> {
  return apiRequest<NetworkResponse>(`/network/${networkId}`, {
    method: 'PUT',
    body: JSON.stringify(networkData),
  });
}

// Fonction pour supprimer un réseau
export async function deleteNetwork(networkId: number): Promise<void> {
  return apiRequest<void>(`/network/${networkId}`, {
    method: 'DELETE',
  });
}

// Fonction pour obtenir le nombre total de réseaux
export async function getNetworkCount(): Promise<number> {
  // Si l'API a un endpoint spécifique pour le comptage
  const response = await apiRequest<CountResponse>('/network/count');
  return response.count;
  
  // Alternative si l'endpoint n'existe pas
  // const networks = await getAllNetworks();
  // return networks.length;
}
