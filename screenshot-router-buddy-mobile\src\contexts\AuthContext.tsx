import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserResponse, getCurrentUser } from '../utils/userService';
import { authenticate, logout as logoutService, TokenResponse } from '../utils/tokenService';

interface AuthContextType {
  isAuthenticated: boolean;
  token: string | null;
  currentUser: UserResponse | null;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshCurrentUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [token, setToken] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [currentUser, setCurrentUser] = useState<UserResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Fonction pour récupérer l'utilisateur actuel
  const refreshCurrentUser = async () => {
    if (token) {
      try {
        const user = await getCurrentUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('Failed to fetch current user:', error);
        // Si l'erreur est due à un token invalide, déconnectez l'utilisateur
        if (error instanceof Error && error.message.includes('Authentication required')) {
          await logout();
        }
      }
    }
  };

  // Initialisation : vérifier si un token existe dans AsyncStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);
        const storedToken = await AsyncStorage.getItem('sems-token');
        
        if (storedToken) {
          setToken(storedToken);
          setIsAuthenticated(true);
          
          // Récupérer les informations de l'utilisateur actuel
          try {
            const user = await getCurrentUser();
            setCurrentUser(user);
          } catch (error) {
            console.error('Failed to get current user on init:', error);
            // Si le token est invalide, nettoyer l'état
            await AsyncStorage.removeItem('sems-token');
            setToken(null);
            setIsAuthenticated(false);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (username: string, password: string) => {
    try {
      console.log('Attempting login for:', username);
      
      // Utiliser le service d'authentification
      const authResponse = await authenticate({ 
        login: username, 
        password 
      });
      
      console.log('Login successful, token received');
      
      // Extraire le token de la réponse
      const jwt = authResponse.accessToken;
      
      // Mettez à jour l'état
      setToken(jwt);
      setIsAuthenticated(true);
      
      // Récupérer les informations de l'utilisateur actuel
      await refreshCurrentUser();
      
    } catch (error) {
      console.error('Login error:', error);
      throw error; // Relancer l'erreur pour que le composant puisse la gérer
    }
  };

  const logout = async () => {
    try {
      // Utiliser le service de déconnexion
      await logoutService();
      
      // Nettoyer l'état local
      setToken(null);
      setIsAuthenticated(false);
      setCurrentUser(null);
      
      console.log('Logout successful');
    } catch (error) {
      console.error('Logout error:', error);
      // Même en cas d'erreur, nettoyer l'état local
      setToken(null);
      setIsAuthenticated(false);
      setCurrentUser(null);
    }
  };

  return (
    <AuthContext.Provider value={{ 
      isAuthenticated, 
      token, 
      currentUser, 
      isLoading,
      login, 
      logout, 
      refreshCurrentUser 
    }}>
      {children}
    </AuthContext.Provider>
  );
}
