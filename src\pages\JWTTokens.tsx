import { useState, useEffect } from 'react';
import { Calendar as CalendarIcon, Copy, Trash, Plus, X, RefreshCw } from 'lucide-react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/sonner';
import { cn } from '@/lib/utils';
import { generateToken, getAllTokens, deleteToken, TokenResponse, TokenCreateRequest } from '@/utils/tokenService';
import { useAuth } from '@/contexts/AuthContext';

interface TokenItem {
  accessToken: string;
  refreshToken: string;
  id?: string; // Ajout d'un ID pour la suppression
}

const JWTTokens = () => {
  const [tokens, setTokens] = useState<TokenItem[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { token } = useAuth();
  
  // État pour le formulaire de création de token
  const [userId, setUserId] = useState<number>(0);
  const [actions, setActions] = useState<string[]>([]);
  const [networkIds, setNetworkIds] = useState<string[]>([]);
  const [deviceTypeIds, setDeviceTypeIds] = useState<string[]>([]);
  
  // État pour les champs temporaires
  const [newAction, setNewAction] = useState('');
  const [newNetworkId, setNewNetworkId] = useState('');
  const [newDeviceTypeId, setNewDeviceTypeId] = useState('');

  // Charger les tokens existants
  const loadTokens = async () => {
    setIsLoading(true);
    try {
      const tokensList = await getAllTokens();
      setTokens(tokensList);
    } catch (error) {
      console.error('Error loading tokens:', error);
      toast.error('Failed to load tokens');
    } finally {
      setIsLoading(false);
    }
  };

  // Charger les tokens au chargement de la page
  useEffect(() => {
    loadTokens();
  }, []);

  const handleGenerateToken = async () => {
    setIsGenerating(true);
    
    try {
      const tokenData: TokenCreateRequest = {
        userId,
        actions,
        networkIds,
        deviceTypeIds
      };
      
      // Utiliser le service pour générer un token
      const newToken = await generateToken(tokenData);
      
      // Ajouter le nouveau token à la liste
      setTokens(prev => [...prev, newToken]);
      
      // Réinitialiser le formulaire
      setUserId(0);
      setActions([]);
      setNetworkIds([]);
      setDeviceTypeIds([]);
      
      toast.success('Token created successfully!');
    } catch (error) {
      console.error('Error creating token:', error);
      toast.error('Failed to create token. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyToken = (token: string) => {
    navigator.clipboard.writeText(token)
      .then(() => toast.success('Token copied to clipboard'))
      .catch(() => toast.error('Failed to copy token'));
  };

  const handleDeleteToken = async (tokenId: string, index: number) => {
    try {
      // Supprimer le token via l'API
      if (tokenId) {
        await deleteToken(tokenId);
      }
      
      // Supprimer de l'état local
      setTokens(tokens.filter((_, i) => i !== index));
      toast.success('Token deleted successfully');
    } catch (error) {
      console.error('Error deleting token:', error);
      toast.error('Failed to delete token');
    }
  };
  
  // Fonctions pour gérer les tableaux
  const addAction = () => {
    if (newAction && !actions.includes(newAction)) {
      setActions([...actions, newAction]);
      setNewAction('');
    }
  };
  
  const removeAction = (action: string) => {
    setActions(actions.filter(a => a !== action));
  };
  
  const addNetworkId = () => {
    if (newNetworkId && !networkIds.includes(newNetworkId)) {
      setNetworkIds([...networkIds, newNetworkId]);
      setNewNetworkId('');
    }
  };
  
  const removeNetworkId = (id: string) => {
    setNetworkIds(networkIds.filter(n => n !== id));
  };
  
  const addDeviceTypeId = () => {
    if (newDeviceTypeId && !deviceTypeIds.includes(newDeviceTypeId)) {
      setDeviceTypeIds([...deviceTypeIds, newDeviceTypeId]);
      setNewDeviceTypeId('');
    }
  };
  
  const removeDeviceTypeId = (id: string) => {
    setDeviceTypeIds(deviceTypeIds.filter(d => d !== id));
  };

  // Afficher le token actuel de l'utilisateur
  const currentToken = token && (
    <div className="mb-6">
      <h3 className="text-lg font-medium mb-2">Your Current Session Token</h3>
      <div className="bg-gray-50 p-4 rounded-md">
        <div className="flex items-center gap-2">
          <code className="bg-gray-100 p-2 rounded text-xs flex-1 overflow-x-auto">
            {token}
          </code>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleCopyToken(token)}
          >
            Copy
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6">
        <h2 className="text-xl font-medium mb-6">JWT Tokens Management</h2>
        
        {currentToken}
        
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-4">Generate New Token</h3>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="userId">User ID</Label>
              <Input 
                id="userId" 
                type="number" 
                value={userId} 
                onChange={(e) => setUserId(parseInt(e.target.value) || 0)}
                className="mt-1"
              />
            </div>
            
            <div>
              <Label>Actions</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {actions.map(action => (
                  <div key={action} className="bg-gray-100 px-2 py-1 rounded-md flex items-center gap-1">
                    <span>{action}</span>
                    <button onClick={() => removeAction(action)} className="text-gray-500 hover:text-red-500">
                      <X size={14} />
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex gap-2 mt-2">
                <Input 
                  value={newAction} 
                  onChange={(e) => setNewAction(e.target.value)}
                  placeholder="Add action"
                />
                <Button type="button" size="sm" onClick={addAction}>
                  <Plus size={16} />
                </Button>
              </div>
            </div>
            
            <div>
              <Label>Network IDs</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {networkIds.map(id => (
                  <div key={id} className="bg-gray-100 px-2 py-1 rounded-md flex items-center gap-1">
                    <span>{id}</span>
                    <button onClick={() => removeNetworkId(id)} className="text-gray-500 hover:text-red-500">
                      <X size={14} />
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex gap-2 mt-2">
                <Input 
                  value={newNetworkId} 
                  onChange={(e) => setNewNetworkId(e.target.value)}
                  placeholder="Add network ID"
                />
                <Button type="button" size="sm" onClick={addNetworkId}>
                  <Plus size={16} />
                </Button>
              </div>
            </div>
            
            <div>
              <Label>Device Type IDs</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {deviceTypeIds.map(id => (
                  <div key={id} className="bg-gray-100 px-2 py-1 rounded-md flex items-center gap-1">
                    <span>{id}</span>
                    <button onClick={() => removeDeviceTypeId(id)} className="text-gray-500 hover:text-red-500">
                      <X size={14} />
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex gap-2 mt-2">
                <Input 
                  value={newDeviceTypeId} 
                  onChange={(e) => setNewDeviceTypeId(e.target.value)}
                  placeholder="Add device type ID"
                />
                <Button type="button" size="sm" onClick={addDeviceTypeId}>
                  <Plus size={16} />
                </Button>
              </div>
            </div>
            
            <Button 
              onClick={handleGenerateToken} 
              className="bg-sems-primary hover:bg-sems-secondary text-white mt-4"
              disabled={isGenerating}
            >
              {isGenerating ? 'Generating...' : 'Generate Token'}
            </Button>
          </div>
        </div>
        
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Existing Tokens</h3>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadTokens} 
              disabled={isLoading}
            >
              <RefreshCw size={16} className={cn("mr-2", isLoading && "animate-spin")} />
              Refresh
            </Button>
          </div>
          
          {isLoading ? (
            <div className="text-center py-8">
              <RefreshCw size={24} className="animate-spin mx-auto mb-2" />
              <p>Loading tokens...</p>
            </div>
          ) : tokens.length > 0 ? (
            <div className="space-y-4">
              {tokens.map((tokenItem, index) => (
                <div key={index} className="bg-gray-50 p-4 rounded-md space-y-2">
                  <div>
                    <h4 className="text-sm font-medium mb-1">Access Token</h4>
                    <div className="flex items-center gap-2">
                      <code className="bg-gray-100 p-2 rounded text-xs flex-1 overflow-x-auto">
                        {tokenItem.accessToken}
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCopyToken(tokenItem.accessToken)}
                      >
                        Copy
                      </Button>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-1">Refresh Token</h4>
                    <div className="flex items-center gap-2">
                      <code className="bg-gray-100 p-2 rounded text-xs flex-1 overflow-x-auto">
                        {tokenItem.refreshToken}
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCopyToken(tokenItem.refreshToken)}
                      >
                        Copy
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex justify-end">
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteToken(tokenItem.id || '', index)}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-md">
              <p className="text-gray-500">No tokens found</p>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default JWTTokens;


