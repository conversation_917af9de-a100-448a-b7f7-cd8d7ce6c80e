import { apiRequest } from './api';

// Interface pour la réponse de l'API token
export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
}

// Interface pour la requête de création de token
export interface TokenCreateRequest {
  userId: number;
  actions: string[];
  networkIds: string[];
  deviceTypeIds: string[];
}

// Interface pour la requête d'authentification
export interface AuthRequest {
  login: string;
  password: string;
}

// Fonction pour s'authentifier et obtenir un token
export async function authenticate(credentials: AuthRequest): Promise<TokenResponse> {
  try {
    console.log('Authenticating with credentials:', { login: credentials.login });
    
    // URL corrigée selon la documentation: /auth/rest/token au lieu de /api/rest/auth/token
    const response = await fetch('/auth/rest/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });
    
    // Ajout de logs pour déboguer
    console.log('Auth response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Auth error (${response.status}):`, errorText);
      throw new Error(`Authentication failed: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('Auth response data:', data);
    
    if (!data || !data.accessToken) {
      console.error('Invalid token response:', data);
      throw new Error('No token returned from server');
    }
    
    return data;
  } catch (error) {
    console.error('Error authenticating:', error);
    throw error;
  }
}

// Fonction pour générer un nouveau token JWT
export async function generateToken(tokenData: TokenCreateRequest): Promise<TokenResponse> {
  // URL corrigée selon la documentation: /auth/rest/token/create
  return apiRequest<TokenResponse>('/auth/rest/token/create', {
    method: 'POST',
    body: JSON.stringify(tokenData),
  });
}

// Fonction pour récupérer tous les tokens
export async function getAllTokens(): Promise<TokenResponse[]> {
  return apiRequest<TokenResponse[]>('/auth/rest/token');
}

// Fonction pour supprimer un token
export async function deleteToken(tokenId: string): Promise<void> {
  return apiRequest<void>(`/auth/rest/token/${tokenId}`, {
    method: 'DELETE',
  });
}




