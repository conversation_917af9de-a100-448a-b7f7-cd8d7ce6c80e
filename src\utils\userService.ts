import { apiRequest } from './api';

// Interface pour les données utilisateur
export interface UserData {
  login: string;
  role: number;
  status: number;
  password?: string;
  data?: {
    jsonString: string;
  };
  introReviewed?: boolean;
}

// Interface pour la réponse de l'API utilisateur
export interface UserResponse {
  id: number;
  login: string;
  role: number;
  status: number;
  lastLogin: string;
  data: {
    jsonString: string;
  };
  introReviewed: boolean;
}

// Fonction pour créer un utilisateur
export async function createUser(userData: UserData): Promise<UserResponse> {
  try {
    console.log('Creating user with data:', { ...userData, password: '***' });
    
    const response = await apiRequest<UserResponse>('/api/rest/user', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
    
    console.log('User created successfully:', response);
    return response;
  } catch (error: any) {
    console.error('Error in createUser service:', error);
    
    // Amélioration des messages d'erreur
    if (error.message.includes('Failed to fetch')) {
      throw new Error('Network error: Unable to connect to the server');
    } else if (error.status === 400) {
      throw new Error('Invalid user data: ' + (error.message || 'Please check your input'));
    } else if (error.status === 401 || error.status === 403) {
      throw new Error('Authentication error: You do not have permission to create users');
    } else if (error.status === 409) {
      throw new Error('User with this login already exists');
    }
    
    throw error;
  }
}

// Fonction pour récupérer un utilisateur par son ID
export async function getUserById(userId: number): Promise<UserResponse> {
  return apiRequest<UserResponse>(`/api/rest/user/${userId}`);
}

// Fonction pour récupérer tous les utilisateurs
export async function getAllUsers(): Promise<UserResponse[]> {
  return apiRequest<UserResponse[]>('/api/rest/user');
}

// Fonction pour mettre à jour un utilisateur
export async function updateUser(userId: number, userData: Partial<UserData>): Promise<UserResponse> {
  return apiRequest<UserResponse>(`/api/rest/user/${userId}`, {
    method: 'PUT',
    body: JSON.stringify(userData),
  });
}

// Fonction pour supprimer un utilisateur
export async function deleteUser(userId: number): Promise<void> {
  return apiRequest<void>(`/api/rest/user/${userId}`, {
    method: 'DELETE',
  });
}

// Fonction pour obtenir le nombre total d'utilisateurs
export async function getUserCount(): Promise<number> {
  // Si l'API ne fournit pas d'endpoint spécifique pour le comptage
  const users = await getAllUsers();
  return users.length;
}

// Fonction pour récupérer l'utilisateur actuellement connecté
export async function getCurrentUser(): Promise<UserResponse> {
  // Si l'API a un endpoint spécifique pour l'utilisateur actuel
  return apiRequest<UserResponse>('/api/rest/user/current');
}







