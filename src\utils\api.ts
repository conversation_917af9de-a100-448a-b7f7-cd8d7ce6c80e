
// Pas de préfixe API_BASE_URL pour permettre l'utilisation de différents chemins de base
// comme /api/rest et /auth/rest
export async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  try {
    // Récupérer le token d'authentification
    const token = localStorage.getItem('sems-token');
    
    // Préparer les en-têtes avec le token d'authentification
    const headers = {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
      ...options.headers,
    };
    
    // Ajouter un timeout pour éviter que la requête ne reste bloquée trop longtemps
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 secondes de timeout
    
    console.log(`API Request: ${options.method || 'GET'} ${endpoint}`);
    
    // Effectuer la requête
    const response = await fetch(endpoint, {
      ...options,
      headers,
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    
    console.log(`API Response: ${response.status} for ${endpoint}`);
    
    // Vérifier si la réponse est OK
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API Error (${response.status}):`, errorText);
      
      // Créer une erreur avec des informations supplémentaires
      const error: any = new Error(`API Error: ${response.status} ${response.statusText}`);
      error.status = response.status;
      error.response = response;
      error.message = errorText;
      throw error;
    }
    
    // Analyser la réponse JSON
    const data = await response.json();
    return data as T;
  } catch (error: any) {
    console.error('API Request Error:', error);
    
    // Amélioration des messages d'erreur
    if (error.name === 'AbortError') {
      throw new Error('Request timeout: The server took too long to respond');
    } else if (error.message.includes('Failed to fetch')) {
      throw new Error('Network error: Unable to connect to the server');
    }
    
    throw error;
  }
}












