import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiRequest, saveAuthToken, removeAuthToken } from './api';

// Interface pour la réponse de l'API token
export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
}

// Interface pour la requête de création de token
export interface TokenCreateRequest {
  userId: number;
  actions: string[];
  networkIds: string[];
  deviceTypeIds: string[];
}

// Interface pour la requête d'authentification
export interface AuthRequest {
  login: string;
  password: string;
}

// Fonction pour s'authentifier et obtenir un token
export async function authenticate(credentials: AuthRequest): Promise<TokenResponse> {
  try {
    console.log('Authenticating with credentials:', { login: credentials.login });
    
    // URL corrigée selon la documentation: /auth/rest/token
    const response = await apiRequest<TokenResponse>('/auth/rest/token', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    
    console.log('Authentication successful, token received');
    
    // Sauvegarder le token dans AsyncStorage
    if (response.accessToken) {
      await saveAuthToken(response.accessToken);
    }
    
    return response;
  } catch (error: any) {
    console.error('Authentication error:', error);
    
    // Amélioration des messages d'erreur
    if (error.status === 401) {
      throw new Error('Invalid credentials. Please check your username and password.');
    } else if (error.status === 403) {
      throw new Error('Access denied. Your account may be disabled.');
    } else if (error.message.includes('Network error')) {
      throw new Error('Unable to connect to the server. Please check your internet connection.');
    }
    
    throw new Error('Authentication failed. Please try again.');
  }
}

// Fonction pour se déconnecter
export async function logout(): Promise<void> {
  try {
    // Supprimer le token du stockage local
    await removeAuthToken();
    console.log('Logout successful');
  } catch (error) {
    console.error('Logout error:', error);
    // Ne pas lancer d'erreur pour la déconnexion
  }
}

// Fonction pour générer un nouveau token JWT
export async function generateToken(tokenData: TokenCreateRequest): Promise<TokenResponse> {
  try {
    return await apiRequest<TokenResponse>('/auth/rest/token/create', {
      method: 'POST',
      body: JSON.stringify(tokenData),
    });
  } catch (error: any) {
    console.error('Token generation error:', error);
    
    if (error.status === 401) {
      throw new Error('Authentication required. Please log in again.');
    } else if (error.status === 403) {
      throw new Error('Insufficient permissions to generate tokens.');
    }
    
    throw new Error('Failed to generate token. Please try again.');
  }
}

// Fonction pour récupérer tous les tokens
export async function getAllTokens(): Promise<TokenResponse[]> {
  try {
    return await apiRequest<TokenResponse[]>('/auth/rest/token');
  } catch (error: any) {
    console.error('Get tokens error:', error);
    
    if (error.status === 401) {
      throw new Error('Authentication required. Please log in again.');
    }
    
    throw new Error('Failed to retrieve tokens. Please try again.');
  }
}

// Fonction pour supprimer un token
export async function deleteToken(tokenId: string): Promise<void> {
  try {
    await apiRequest<void>(`/auth/rest/token/${tokenId}`, {
      method: 'DELETE',
    });
  } catch (error: any) {
    console.error('Delete token error:', error);
    
    if (error.status === 401) {
      throw new Error('Authentication required. Please log in again.');
    } else if (error.status === 404) {
      throw new Error('Token not found.');
    }
    
    throw new Error('Failed to delete token. Please try again.');
  }
}

// Fonction pour vérifier si l'utilisateur est authentifié
export async function isAuthenticated(): Promise<boolean> {
  try {
    const token = await AsyncStorage.getItem('sems-token');
    return !!token;
  } catch (error) {
    console.error('Error checking authentication status:', error);
    return false;
  }
}
