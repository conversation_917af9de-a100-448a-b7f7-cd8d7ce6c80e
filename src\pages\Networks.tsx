
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Input } from '@/components/ui/input';
import TableActions from '@/components/TableActions';
import { toast } from '@/components/ui/sonner';
import { getAllNetworks, NetworkResponse, deleteNetwork } from '@/utils/networkService';

const Networks = () => {
  const [networks, setNetworks] = useState<NetworkResponse[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchNetworks = async () => {
      try {
        setLoading(true);
        const networkData = await getAllNetworks();
        console.log('Networks fetched:', networkData);
        setNetworks(networkData);
      } catch (error) {
        console.error('Error fetching networks:', error);
        toast.error('Failed to load networks');
      } finally {
        setLoading(false);
      }
    };

    fetchNetworks();
  }, []);

  const filteredNetworks = networks.filter(network =>
    network.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddNetwork = () => {
    navigate('/admin/networks/add');
  };

  const handleEditNetwork = (networkId: number) => {
    navigate(`/admin/networks/${networkId}`);
  };

  const handleDeleteNetwork = async (networkId: number) => {
    if (window.confirm('Are you sure you want to delete this network?')) {
      try {
        await deleteNetwork(networkId);
        setNetworks(networks.filter(network => network.id !== networkId));
        toast.success('Network deleted successfully');
      } catch (error) {
        console.error('Error deleting network:', error);
        toast.error('Failed to delete network');
      }
    }
  };

  return (
    <AdminLayout
      actionButton={{
        label: 'Add New Network',
        onClick: handleAddNetwork,
      }}
    >
      <div className="bg-white rounded-md shadow-sm">
        <div className="p-4 border-b">
          <div className="flex items-center gap-2">
            <span>Search By Name:</span>
            <Input
              placeholder="Enter network name"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 text-left">
              <tr>
                <th className="p-4 font-medium">Name</th>
                <th className="p-4 font-medium">Description</th>
                <th className="p-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y">
              {loading ? (
                <tr>
                  <td colSpan={3} className="p-4 text-center">Loading networks...</td>
                </tr>
              ) : filteredNetworks.length === 0 ? (
                <tr>
                  <td colSpan={3} className="p-4 text-center">No networks found</td>
                </tr>
              ) : (
                filteredNetworks.map((network) => (
                  <tr key={network.id} className="hover:bg-gray-50">
                    <td className="p-4">{network.name}</td>
                    <td className="p-4">{network.description}</td>
                    <td className="p-4">
                      <TableActions 
                        onEdit={() => handleEditNetwork(network.id)}
                        onDelete={() => handleDeleteNetwork(network.id)}
                      />
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Networks;

